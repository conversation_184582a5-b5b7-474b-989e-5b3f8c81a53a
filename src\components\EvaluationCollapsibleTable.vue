<script setup lang="ts">
import { ref } from 'vue';

// Mock data structure
const groups = [
  {
    group: 'Fashion',
    subtotal: {
      store: 'Dalma',
      grp_pm: 'Fashion Total',
      dpt_nm: 'Fashion Total',
      rtl_qty: { lywtd: 2924, cywtd: 86844, growth: '68%' },
      revenue: { lywtd: 2924, cywtd: 86844, growth: '68%' },
      gmv: { lywtd: 2924, cywtd: 86844, growth: '68%' },
      invoice: { lywtd: 2924, cywtd: 86844, growth: '68%' },
    },
    rows: [
      {
        store: 'Dalma',
        grp_pm: 'Fashion',
        dpt_nm: 'Bags',
        rtl_qty: { lywtd: 473, cywtd: 59582, growth: '68%' },
        revenue: { lywtd: 473, cywtd: 59582, growth: '68%' },
        gmv: { lywtd: 473, cywtd: 59582, growth: '68%' },
        invoice: { lywtd: 473, cywtd: 59582, growth: '68%' },
      },
      {
        store: 'Dalma',
        grp_pm: 'Fashion',
        dpt_nm: 'Fashion Accessories',
        rtl_qty: { lywtd: 1089, cywtd: 15272, growth: '-18%' },
        revenue: { lywtd: 1089, cywtd: 15272, growth: '-18%' },
        gmv: { lywtd: 1089, cywtd: 15272, growth: '-18%' },
        invoice: { lywtd: 1089, cywtd: 15272, growth: '-18%' },
      },
      {
        store: 'Dalma',
        grp_pm: 'Fashion',
        dpt_nm: 'Hair Accessories',
        rtl_qty: { lywtd: 1352, cywtd: 11990, growth: '68%' },
        revenue: { lywtd: 1352, cywtd: 11990, growth: '68%' },
        gmv: { lywtd: 1352, cywtd: 11990, growth: '68%' },
        invoice: { lywtd: 1352, cywtd: 11990, growth: '68%' },
      },
    ],
  },
  {
    group: 'Home',
    subtotal: {
      store: 'Dalma',
      grp_pm: 'Home Total',
      dpt_nm: 'Home Total',
      rtl_qty: { lywtd: 2924, cywtd: 86844, growth: '35%' },
      revenue: { lywtd: 2924, cywtd: 86844, growth: '35%' },
      gmv: { lywtd: 2924, cywtd: 86844, growth: '35%' },
      invoice: { lywtd: 2924, cywtd: 86844, growth: '35%' },
    },
    rows: [
      {
        store: 'Dalma',
        grp_pm: 'Home',
        dpt_nm: 'Bathroom',
        rtl_qty: { lywtd: 488, cywtd: 18228, growth: '68%' },
        revenue: { lywtd: 488, cywtd: 18228, growth: '68%' },
        gmv: { lywtd: 488, cywtd: 18228, growth: '68%' },
        invoice: { lywtd: 488, cywtd: 18228, growth: '68%' },
      },
      {
        store: 'Dalma',
        grp_pm: 'Home',
        dpt_nm: 'Floor Covering',
        rtl_qty: { lywtd: 28, cywtd: 2280, growth: '-18%' },
        revenue: { lywtd: 28, cywtd: 2280, growth: '-18%' },
        gmv: { lywtd: 28, cywtd: 2280, growth: '-18%' },
        invoice: { lywtd: 28, cywtd: 2280, growth: '-18%' },
      },
      {
        store: 'Dalma',
        grp_pm: 'Home',
        dpt_nm: 'Home Decor',
        rtl_qty: { lywtd: 13875, cywtd: 13875, growth: '-18%' },
        revenue: { lywtd: 13875, cywtd: 13875, growth: '-18%' },
        gmv: { lywtd: 13875, cywtd: 13875, growth: '-18%' },
        invoice: { lywtd: 13875, cywtd: 13875, growth: '-18%' },
      },
      {
        store: 'Dalma',
        grp_pm: 'Home',
        dpt_nm: 'Soft Furnishing',
        rtl_qty: { lywtd: 6058, cywtd: 6058, growth: '-18%' },
        revenue: { lywtd: 6058, cywtd: 6058, growth: '-18%' },
        gmv: { lywtd: 6058, cywtd: 6058, growth: '-18%' },
        invoice: { lywtd: 6058, cywtd: 6058, growth: '-18%' },
      },
      {
        store: 'Dalma',
        grp_pm: 'Home',
        dpt_nm: 'Tabletop',
        rtl_qty: { lywtd: 13726, cywtd: 13726, growth: '28%' },
        revenue: { lywtd: 13726, cywtd: 13726, growth: '28%' },
        gmv: { lywtd: 13726, cywtd: 13726, growth: '28%' },
        invoice: { lywtd: 13726, cywtd: 13726, growth: '28%' },
      },
    ],
  },
];

const expandedGroups = ref<string[]>([]);

function toggleGroup(group: string) {
  const idx = expandedGroups.value.indexOf(group);
  if (idx > -1) expandedGroups.value.splice(idx, 1);
  else expandedGroups.value.push(group);
}
function isGroupExpanded(group: string) {
  return expandedGroups.value.includes(group);
}
function growthClass(val: string) {
  if (val.includes('-')) return 'text-red-400 font-bold';
  if (val === '0%' || val === '0') return 'text-gray-400';
  return 'text-green-400 font-bold';
}
function growthArrow(val: string) {
  const num = parseFloat(val);
  if (isNaN(num)) return '';
  if (num > 0) return '▲';
  if (num < 0) return '▼';
  return '';
}
</script>

<template>
  <table class="min-w-full  text-sm border border-gray-300">
    <thead>
      <tr class="bg-[#e5eaf1]">
        <th class="px-2 py-2 text-[#232837] text-center font-bold ">Store</th>
        <th class="px-2 py-2 text-[#232837] text-center font-bold ">GRP_NM</th>
        <th class="px-2 py-2 text-[#232837] text-center font-bold border-r border-gray-300">DPT_NM</th>
        <th colspan="3" class="px-2 py-2 text-[#232837] text-center font-bold border-r border-gray-300">RTL Quantity</th>
        <th colspan="3" class="px-2 py-2 text-[#232837] text-center font-bold border-r border-gray-300">Revenue</th>
        <th colspan="3" class="px-2 py-2 text-[#232837] text-center font-bold border-r border-gray-300">GMV</th>
        <th colspan="3" class="px-2 py-2 text-[#232837] text-center font-bold">Invoice</th>
      </tr>
      <tr class="bg-[#e5eaf1] text-xs font-bold uppercase text-center">
        <th></th>
        <th></th>
        <th></th>
        <th class="px-2 py-1 border-t border-r border-l border-gray-300">LY WTD</th>
        <th class="px-2 py-1 border-t border-r border-gray-300">CY WTD</th>
        <th class="px-2 py-1 border-t border-r border-gray-300">Growth</th>
        <th class="px-2 py-1 border-t border-gray-300">LY WTD</th>
        <th class="px-2 py-1 border-t border-gray-300">CY WTD</th>
        <th class="px-2 py-1 border-t border-r border-gray-300">Growth</th>
        <th class="px-2 py-1 border-t border-gray-300">LY WTD</th>
        <th class="px-2 py-1 border-t border-gray-300">CY WTD</th>
        <th class="px-2 py-1 border-t border-r border-gray-300">Growth</th>
        <th class="px-2 py-1 border-t border-gray-300">LY WTD</th>
        <th class="px-2 py-1 border-t border-gray-300">CY WTD</th>
        <th class="px-2 py-1 border-t border-gray-300">Growth</th>
      </tr>
    </thead>
    <tbody>
      <template v-for="group in groups" :key="group.group">
        <!-- Subtotal Row (collapsible) -->
        <tr @click="toggleGroup(group.group)" class="bg-gray-50 font-bold cursor-pointer hover:bg-gray-100 transition-colors">
          <td class="px-2 py-2 border-r border-gray-300 text-center">
            <span class="flex items-center justify-center">
              <span class="mr-2">
                <svg v-if="isGroupExpanded(group.group)" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </span>
              {{ group.subtotal.store }}
            </span>
          </td>
          <td class="px-2 py-2 border-r border-gray-300 text-center">{{ group.subtotal.grp_pm }}</td>
          <td class="px-2 py-2 border-r border-gray-300 text-center"></td>
          <td class="px-2 py-2 text-center">{{ group.subtotal.rtl_qty.lywtd.toLocaleString() }}</td>
          <td class="px-2 py-2 text-center">{{ group.subtotal.rtl_qty.cywtd.toLocaleString() }}</td>
          <td class="px-2 py-2 text-center border-r border-gray-300">
            <span :class="growthClass(group.subtotal.rtl_qty.growth)">
              {{ growthArrow(group.subtotal.rtl_qty.growth) }} {{ group.subtotal.rtl_qty.growth }}
            </span>
          </td>
          <td class="px-2 py-2 text-center">{{ group.subtotal.revenue.lywtd.toLocaleString() }}</td>
          <td class="px-2 py-2 text-center">{{ group.subtotal.revenue.cywtd.toLocaleString() }}</td>
          <td class="px-2 py-2 text-center border-r border-gray-300">
            <span :class="growthClass(group.subtotal.revenue.growth)">
              {{ growthArrow(group.subtotal.revenue.growth) }} {{ group.subtotal.revenue.growth }}
            </span>
          </td>
          <td class="px-2 py-2 text-center">{{ group.subtotal.gmv.lywtd.toLocaleString() }}</td>
          <td class="px-2 py-2 text-center">{{ group.subtotal.gmv.cywtd.toLocaleString() }}</td>
          <td class="px-2 py-2 text-center border-r border-gray-300">
            <span :class="growthClass(group.subtotal.gmv.growth)">
              {{ growthArrow(group.subtotal.gmv.growth) }} {{ group.subtotal.gmv.growth }}
            </span>
          </td>
          <td class="px-2 py-2 text-center">{{ group.subtotal.invoice.lywtd.toLocaleString() }}</td>
          <td class="px-2 py-2 text-center">{{ group.subtotal.invoice.cywtd.toLocaleString() }}</td>
          <td class="px-2 py-2 text-center">
            <span :class="growthClass(group.subtotal.invoice.growth)">
              {{ growthArrow(group.subtotal.invoice.growth) }} {{ group.subtotal.invoice.growth }}
            </span>
          </td>
        </tr>
        <!-- Department Rows (expandable) -->
        <template v-if="isGroupExpanded(group.group)">
          <tr v-for="row in group.rows" :key="row.dpt_nm" class="bg-white">
            <td class="px-2 py-2 border-r border-gray-300 text-center">{{ row.store }}</td>
            <td class="px-2 py-2 border-r border-gray-300 text-center">{{ row.grp_pm }}</td>
            <td class="px-2 py-2 border-r border-gray-300 text-center"></td>
            <td class="px-2 py-2 text-center">{{ row.rtl_qty.lywtd.toLocaleString() }}</td>
            <td class="px-2 py-2 text-center">{{ row.rtl_qty.cywtd.toLocaleString() }}</td>
            <td class="px-2 py-2 text-center border-r border-gray-300">
              <span :class="growthClass(row.rtl_qty.growth)">
                {{ growthArrow(row.rtl_qty.growth) }} {{ row.rtl_qty.growth }}
              </span>
            </td>
            <td class="px-2 py-2 text-center">{{ row.revenue.lywtd.toLocaleString() }}</td>
            <td class="px-2 py-2 text-center">{{ row.revenue.cywtd.toLocaleString() }}</td>
            <td class="px-2 py-2 text-center border-r border-gray-300">
              <span :class="growthClass(row.revenue.growth)">
                {{ growthArrow(row.revenue.growth) }} {{ row.revenue.growth }}
              </span>
            </td>
            <td class="px-2 py-2 text-center">{{ row.gmv.lywtd.toLocaleString() }}</td>
            <td class="px-2 py-2 text-center">{{ row.gmv.cywtd.toLocaleString() }}</td>
            <td class="px-2 py-2 text-center border-r border-gray-300">
              <span :class="growthClass(row.gmv.growth)">
                {{ growthArrow(row.gmv.growth) }} {{ row.gmv.growth }}
              </span>
            </td>
            <td class="px-2 py-2 text-center">{{ row.invoice.lywtd.toLocaleString() }}</td>
            <td class="px-2 py-2 text-center">{{ row.invoice.cywtd.toLocaleString() }}</td>
            <td class="px-2 py-2 text-center">
              <span :class="growthClass(row.invoice.growth)">
                {{ growthArrow(row.invoice.growth) }} {{ row.invoice.growth }}
              </span>
            </td>
          </tr>
        </template>
      </template>
    </tbody>
  </table>
</template> 