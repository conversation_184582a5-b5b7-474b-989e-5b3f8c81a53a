// tailwind.config.js
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['"SFProText"', 'ui-sans-serif', 'system-ui'],
      },
      colors: {
        sidebar: "#2C3E50",
        "sidebar-hover": "#34495E",
        "header-bg": "#FFFFFF",
        "main-bg": "#F8FAFC",
        "card-bg": "#FFFFFF",
        "positive": "#10B981",
        "negative": "#EF4444",
        "neutral": "#F59E0B",
        "table-header": "#F1F5F9",
        "table-row-hover": "#F1F5F9",
        "table-border": "#E2E8F0",
      },
    },
  },
  plugins: [],
}
