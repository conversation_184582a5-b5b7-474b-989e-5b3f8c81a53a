{"name": "vite-vue-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^4.13.0", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "apexcharts": "^4.7.0", "axios": "^1.10.0", "date-fns": "^2.30.0", "lucide-vue-next": "^0.513.0", "plotly.js-dist-min": "^3.0.1", "vue": "^3.4.38", "vue-multiselect": "^3.2.0", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.8.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.3", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}