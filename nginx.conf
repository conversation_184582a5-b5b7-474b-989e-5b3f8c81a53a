server {
		listen 80;

		# Reverse proxy API requests to the backend service
		location /api {
            rewrite ^/api(.*)$ /api/v1$1 break;
			proxy_pass http://backend;
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-Proto $scheme;
			proxy_redirect off;
		}

        location / {
			root /usr/share/nginx/html;
			index index.html;
			try_files $uri $uri/ /index.html;
		}


		location /assets {
			root /usr/share/nginx/html;
			try_files $uri $uri/ assets/$uri =200;
		}

} 