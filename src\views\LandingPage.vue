<template>
  <div class="min-h-screen bg-slate-50 flex flex-col">
    <!-- Header -->
    <header class="bg-teal-800 text-white p-4 flex justify-between items-center">
      <div class="flex items-center pl-10">
        <img src="/src/assets/piechart.svg" alt="SpaceSense Logo" class="h-8 w-8 mr-2" />
        <div class="pl-2">
          <h1 class="text-xl font-semibold">Space Sense</h1>
          <p class="text-sm">Space Optimization System</p>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow flex items-center justify-center">
      <div class="bg-white p-8 rounded-lg shadow-md text-center">
        <h2 class="text-3xl font-bold text-gray-800 mb-2">Sign in to your account</h2>
        <p class="text-gray-600 mb-6">Access the SpaceSense Optimization system</p>

        <button @click="signIn" class="bg-teal-800 hover:bg-teal-700 text-white font-bold py-3 px-6 rounded-lg flex items-center justify-center mb-4 w-full">
          <img src="../assets/microsoft-icon.svg" alt="Microsoft Icon" class="h-5 w-5 mr-3" />
          Sign in with Microsoft
        </button>
      </div>
    </main>

    <!-- Footer -->
    <footer class="bg-teal-700 text-white p-4 text-center text-sm">
      <p>Data Labs Landmark | All rights reserved</p>
    </footer>
  </div>
</template>

<script setup lang="ts">

const redirectUri = 'http://localhost:5173/spaceoptimization/callback';

const signIn = () => {
  const clientId = '6cbe6000-9457-40e5-9186-50183ba4ff94';
  const tenantId = '1f9b09b4-197c-4f1c-b0c5-571a6ccc96c8';
  const scopes = [
  'openid',
  'profile',
  'email',
  'User.Read',
  'offline_access'
].join(' ');

const authorizeUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/authorize` +
  `?client_id=${clientId}` +
  `&response_type=code` +
  `&redirect_uri=${encodeURIComponent(redirectUri)}` +
  `&scope=${encodeURIComponent(scopes)}` +
  `&response_mode=query`;

  window.location.href = authorizeUrl;
};
</script>

<style scoped>
/* Add any specific styles here if needed */
</style> 