apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: space-optimization-ingress
  namespace: dll-space-optimization
  annotations:
    
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    
    nginx.ingress.kubernetes.io/use-regex: "true"
    
    service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path: /healthz
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
   
   
spec:
  ingressClassName: nginx
  tls:
    - secretName: ingress-tls-csi
      hosts:
        - lmdllcloudapp.landmarkgroup.com
  rules:
    - host: lmdllcloudapp.landmarkgroup.com
      http:
        paths:
          - path: /spaceoptimization/(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: space-optimization-frontend-service
                port:
                  name: http