<script setup lang="ts">
import Sidebar from './Sidebar.vue';
import SpaceHealthTable from './SpaceHealthTables.vue';
import SpaceHealthFilterBar from './SpaceHealthFilterBar.vue';
import FilterBar from './FilterBar.vue';
</script>

<template>
  <div class="flex h-screen w-full bg-main-bg">
    <div class="flex-1 flex flex-col overflow-hidden">
      <div class="bg-[#232837]">
        <div class="pl-12 pt-8 pb-2">
          <div class="uppercase text-xs font-semibold text-gray-300 tracking-widest">
            SPACE RECOMMENDATION
          </div>
          <h1 class="text-2xl font-bold text-white mt-2">
            Space Health Dashboard
          </h1>
          <div class="mt-6 mr-8">
            <FilterBar />
          </div>
        </div>
      </div>
      <main class="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8 ">
        <div class="bg-white rounded-lg shadow">
          <div class="mt-4 table-container">
            <SpaceHealthTable />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>