<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import Dashboard from './components/Optimization.vue';
import Sidebar from './components/Sidebar.vue'
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'

const isExpanded = ref(false)
const route = useRoute();
const shouldShowSidebar = computed(() => route.path !== '/' && route.path !== '/concept-selection');

const toggleSidebar = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<template>
  <div class="flex h-screen bg-gray-50 relative font-sans">
    <Sidebar v-if="shouldShowSidebar" :isExpanded="isExpanded" />
    <button
      v-if="shouldShowSidebar"
      @click="toggleSidebar"
      class="py-1 bg-white hover:bg-gray-100 transition-colors absolute"
      :style="{ left: isExpanded ? '280px' : '100px', top: '18px' }"
    >
      <ChevronLeft v-if="isExpanded" class="w-4 h-4 text-gray-600" />
      <ChevronRight v-else class="w-4 h-4 text-gray-600" />
    </button>
    <main :class="shouldShowSidebar ? 'flex-1 overflow-auto' : 'flex-1 overflow-auto w-full'">
      <router-view />
    </main>
  </div>
</template>