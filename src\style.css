@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: #213547;
  background-color: #F8FAFC;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@font-face {
  font-family: "SFProText";
  font-weight: 700;
  src: url("/src/assets/fonts/SFProText-Bold.woff2") format("woff2"),
       url("/src/assets/fonts/SFProText-Bold.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "SFProText";
  font-weight: 600;
  src: url("/src/assets/fonts/SFProText-Semibold.woff2") format("woff2"),
       url("/src/assets/fonts/SFProText-Semibold.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "SFProText";
  font-weight: 500;
  src: url("/src/assets/fonts/SFProText-Medium.woff2") format("woff2"),
       url("/src/assets/fonts/SFProText-Medium.woff") format("woff");
  font-display: swap;
}



@font-face {
  font-family: "SFProText";
  font-weight: 400;
  src: url("/src/assets/fonts/SFProText-Regular.woff2") format("woff2"),
       url("/src/assets/fonts/SFProText-Regular.woff") format("woff");
  font-display: swap;
}

body {
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
  /* font-family: "SFProText", -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; */
  font-weight: 400;
}

#app {
  width: 100%;
  height: 100vh;
}

@layer components {
  .filter-dropdown {
    @apply bg-white border border-gray-300 text-sm rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }

  .filter-date {
    @apply bg-white border border-gray-300 text-sm rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }

  .filter-btn {
    @apply bg-[#7DD3FC] hover:bg-[#38BDF8] text-white font-medium py-2 px-6 rounded-md transition-colors;
  }
}

.table-container {
  overflow-x: auto;
  scrollbar-width: thin;
}

.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

.bg-gradient-header {
  background: linear-gradient(180deg, #FFFFFF 0%, #F8FAFC 100%);
}