<script setup lang="ts">
import { ref } from 'vue';
import { 
  ChevronLeft, 
  ChevronRight,
  FileText,
  BarChart2,
  Settings,
  ClipboardList,
  Bell,
  Info,
  LogOut,
  Gauge,
  Boxes
} from 'lucide-vue-next'
import PieChartIcon from '../assets/piechart.svg'; // Updated path to the SVG logo

const props = defineProps({
  isExpanded: Boolean
})

const notificationCount = ref(3)

const menuItems =  [
   {
    label: 'Store Clustering​',
    path: '/store-clustering',
    icon: Boxes
  },
  {
    label: 'Business Requirements',
    path: '/business-requirements',
    icon: FileText
  },
    {
    label: 'Space Health Dashboard​',
    path: '/space-health',
    icon: Gauge
  },
  {
    label: 'Saturation Point',
    path: '/saturation-point',
    icon: BarChart2
  },
  {
    label: 'Optimization Summary',
    path: '/optimization',
    icon: Settings
  },
  // {
  //   label: 'Evaluation Summary',
  //   path: '/evaluation-dashboard',
  //   icon: ClipboardList
  // }
]
</script>

<template>
  <div
    class="h-screen flex flex-col bg-slate-800 transition-all duration-300"
    :style="{ width: isExpanded ? '280px' : '101px' }"
  >
    <!-- Top Section: Logo -->
    <div class="flex items-center p-4" :class="{ 'justify-center': !isExpanded }">
      <div class="w-10 h-10 bg-[#F9FAFB] rounded-full flex items-center justify-center">
        <img :src="PieChartIcon" class="w-6 h-6 text-white" alt="Space Sense Logo" />
      </div>
      <span v-if="isExpanded" class="ml-3 text-white text-lg font-semibold whitespace-nowrap">Space Sense</span>
    </div>

    <!-- Main Navigation Items -->
    <nav class="flex-1 mt-6 space-y-2 px-3">
      <router-link
        v-for="item in menuItems"
        :key="item.path"
        :to="item.path"
        class="flex items-center py-2 px-3 rounded-lg text-white hover:bg-slate-700 transition-colors"
        :class="{ 'justify-center': !isExpanded }"
      >
        <component :is="item.icon" class="w-5 h-5" />
        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap">
          {{ item.label }}
        </span>
      </router-link>
    </nav>

    <!-- Bottom Section: Notifications, Info, Settings, Logout, User Profile -->
    <!-- <div class="mt-auto pb-4 space-y-2 px-3"> -->
      <!-- Notifications -->
      <!-- <router-link
        to="/#"
        class="flex items-center py-2 px-3 rounded-lg text-white hover:bg-slate-700 transition-colors relative"
        :class="{ 'justify-center': !isExpanded }"
      >
        <Bell class="w-5 h-5" />

        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap">
          Notifications
        </span>
      </router-link> -->

      <!-- Information -->
      <!-- <router-link
        to="/#"
        class="flex items-center py-2 px-3 rounded-lg text-white hover:bg-slate-700 transition-colors"
        :class="{ 'justify-center': !isExpanded }"
      >
        <Info class="w-5 h-5" />
        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap">
          Information
        </span>
      </router-link> -->

      <!-- Settings -->
      <router-link
        to="/#"
        class="flex items-center py-2 px-3 rounded-lg text-white hover:bg-slate-700 transition-colors"
        :class="{ 'justify-center': !isExpanded }"
      >
        <Settings class="w-5 h-5" />
        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap">
          Settings
        </span>
      </router-link>

      <!-- Log out -->
      <router-link
        to="/#"
        class="flex items-center py-2 px-3 rounded-lg text-white hover:bg-slate-700 transition-colors"
        :class="{ 'justify-center': !isExpanded }"
      >
        <LogOut class="w-5 h-5" />
        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap">
          Log out
        </span>
      </router-link>

      <!-- User Profile -->
      <div
        class="mt-4 flex items-center py-2 px-3 rounded-lg bg-slate-700 text-white"
        :class="{ 'justify-center': !isExpanded }"
      >
        <div class="w-8 h-8 flex items-center justify-center bg-gray-500 rounded-full text-sm font-bold">
         PT
        </div>
        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap">
          Prakhar Tripathi
        </span>
      </div>



    
  </div>
</template>


