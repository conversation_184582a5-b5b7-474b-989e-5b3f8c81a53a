<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import TableRow from './TableRow.vue';
import GroupTotalRow from './GroupTotalRow.vue';
import { storeData } from '../data/storeData';

const sortColumn = ref('');
const sortDirection = ref('asc');
const expandedGroups = ref<string[]>([]);

const sortData = (column: string) => {
  if (sortColumn.value === column) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortColumn.value = column;
    sortDirection.value = 'asc';
  }
};

const toggleGroup = (group: string) => {
  const index = expandedGroups.value.indexOf(group);
  if (index > -1) {
    expandedGroups.value.splice(index, 1);
  } else {
    expandedGroups.value.push(group);
  }
};

const groupedData = computed(() => {
  const groups: Record<string, any[]> = {};
  
  storeData.forEach(item => {
    if (!groups[item.group]) {
      groups[item.group] = [];
    }
    groups[item.group].push(item);
  });
  
  return groups;
});

const groupTotals = computed(() => {
  const totals: Record<string, any> = {};
  
  Object.keys(groupedData.value).forEach(group => {
    const items = groupedData.value[group];
    
    totals[group] = {
      storeCode: '21404',
      storeName: 'Dalma Mall',
      group: `${group} Total`,
      productivity: items.reduce((sum, item) => sum + item.productivity, 0) / items.length,
      currentLm: items.reduce((sum, item) => sum + item.currentLm, 0),
      optimizedLm: items.reduce((sum, item) => sum + item.optimizedLm, 0),
      optimizedGmv: items.reduce((sum, item) => sum + item.optimizedGmv, 0),
      gmvChange: (items.reduce((sum, item) => sum + item.optimizedGmv, 0) / 
                  items.reduce((sum, item) => sum + item.gmv, 0) - 1) * 100
    };
  });
  
  return totals;
});

const isGroupExpanded = (group: string) => {
  return expandedGroups.value.includes(group);
};

onMounted(() => {
  expandedGroups.value = [];
});
</script>

<template>
  <table class="min-w-full divide-y divide-gray-200 " >
    <thead class="bg-[#e5eaf1]">
      <tr>
        <th v-for="(header, index) in [
          'Store Code', 'Store Name', 'Group', 'Department', 'Class', 'Subclass',
          'Productivity', 'GMV/LM Rank', 'Space Rank', 'Recommendation', 'Min',
          'Max', 'Current LM', 'Optimized LM', 'Space Change', 'Optimized GMV',
          'GMV Change'
        ]" 
        :key="index"
        @click="sortData(header.toLowerCase())"
        class="px-6 py-3 text-left text-xs font-bold text-[#232837] uppercase tracking-wider cursor-pointer"
        :class="{ 'text-right': index >= 6 && index !== 9 }">
          {{ header }}
          <span v-if="sortColumn === header.toLowerCase()" class="ml-1">
            {{ sortDirection === 'asc' ? '↑' : '↓' }}
          </span>
        </th>
      </tr>
    </thead>
    <tbody>
      <template v-for="(items, group) in groupedData" :key="group">
        <GroupTotalRow 
          :group="group"
          :totals="groupTotals[group]"
          :is-expanded="isGroupExpanded(group)"
          @toggle="toggleGroup(group)"
          class="even:bg-[#f5faff] odd:bg-white"
        />
        <template v-if="isGroupExpanded(group)">
          <TableRow 
            v-for="(item, idx) in items.slice().reverse()"
            :key="`${item.storeCode}-${item.department}`"
            :item="item"
            :class="idx % 2 === 0 ? 'bg-white' : 'bg-[#f5faff]'"
          />
        </template>
      </template>
    </tbody>
  </table>
</template>

<style scoped>
table {
  border-collapse: separate;
  border-spacing: 0;
}

th {
  position: sticky;
  top: 0;
  z-index: 10;
  transition: background-color 0.2s;
}

th:hover {
  background-color: #E2E8F0;
}
</style>