<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import VueApexCharts from 'vue3-apexcharts';

interface ChartDataPoint {
  x: number;
  y: number;
}

const props = defineProps({
  predictedData: { type: Array as () => ChartDataPoint[], required: true },
  originalData: { type: Array as () => ChartDataPoint[], required: true },
  r2: { type: Number, required: true },
  ridgeR2: { type: Number, required: true },
  diff: { type: Number, required: true },
  title: { type: String, required: true },
  parabolaOnly: { type: Boolean, default: false }
});

const chartOptions = ref({});
const series = ref<any[]>([]);
const maxPoint = ref<{ x: number; y: number }>({ x: 0, y: 0 });

function updateChart() {
  const validPredicted = props.predictedData.filter(p => isFinite(p.x) && isFinite(p.y));
  const maxY = Math.max(...validPredicted.map(p => p.y));
  const max = validPredicted.find(p => p.y === maxY);
  maxPoint.value = max ?? { x: 0, y: 0 };

  series.value = [
    {
      name: 'Predicted Productivity',
      type: 'line',
      data: validPredicted,
      color: '#007bff'
    },
    {
      name: 'Original Data',
      type: 'scatter',
      data: props.originalData,
      color: '#dc2626'
    }
  ];

  chartOptions.value = {
    chart: {
      height: 350,
      type: 'line',
      toolbar: {
        show: false
      }
    },
    title: {
      text: props.title,
      align: 'center',
      style: {
        fontSize: '16px',
        fontWeight: 600,
        fontFamily: 'SFProText'
      }
    },
    annotations: {
      points: [{
        x: maxPoint.value.x,
        y: maxPoint.value.y,
        marker: {
          size: 0 // Hide the default marker
        },
        label: {
          borderColor: '#059669',
          borderWidth: 1,
          borderRadius: 5,
          text: `Saturation Point: ${maxPoint.value.x.toFixed(1)}`,
          style: {
            color: '#059669',
            background: '#ffffff',
            padding: {
              left: 5,
              right: 5,
              top: 2,
              bottom: 2
            },
            fontSize: '12px',
            fontWeight: 400
          },
          offsetY: -15
        }
      }],
      xaxis: [{
        x: maxPoint.value.x,
        strokeDashArray: 5,
        borderColor: '#059669'
      }]
    },
    xaxis: {
      title: { text: 'Linear Meter (LM)' },
      type: 'numeric',
      tickAmount: 5,
      labels: {
        formatter: (value: number) => value.toFixed(1)
      },
      axisTicks: { show: true },
      axisBorder: { show: true },
      tickPlacement: 'on',
      forceNiceScale: true,
      min: Math.min(...props.predictedData.map((p) => p.x)),
      max: Math.max(...props.predictedData.map((p) => p.x))
    },
    yaxis: {
      title: { text: 'Productivity' },
      tickAmount: 5,
      labels: {
        formatter: (value: number) => value.toFixed(1)
      }
    },
    markers: {
      size: [0, 5, 8],
      colors: ['#007bff', '#dc2626', '#0047AB'],
      strokeColors: ['#fff', '#fff', '#fff'],
      strokeWidth: [0, 2, 3],
      hover: {
        size: [0, 5, 8], // No size increase on hover
        sizeOffset: 0
      }
    },
    stroke: {
      width: [3, 0, 0],
      curve: 'smooth'
    },
    legend: {
      show: true,
      position: 'top',
      horizontalAlign: 'right'
    },
    tooltip: {
      enabled: false // Disable all tooltips
    }
  };
}

onMounted(updateChart);
watch(props, updateChart, { deep: true });
</script>

<template>
  <div class="relative">
    <!-- Apex Chart -->
    <VueApexCharts type="line" height="350" :options="chartOptions" :series="series" />

    <!-- Permanent Label Above the Blue Dot -->
 

    <!-- R² Metrics -->
    <div class="bg-[#e5eaf1] text-center text-xl font-bold py-2 mt-2 rounded">
      R² : {{ r2 }} % | Ridge R² : {{ ridgeR2 }} % | difference : {{ diff }} %
    </div>
  </div>
</template>

