<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
    <div class="max-w-7xl mx-auto">
      
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Space Recommendation Form</h1>
      </div>

      <!-- Form Container -->
      <div class="bg-white rounded-xl shadow-lg p-8">
        
        <!-- Two Column Layout -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          
          <!-- Row 1: Space Recommendation Levels & Season Preference -->
          <div>
            <label class="block text-lg font-semibold text-gray-800 mb-4">
              Space Recommendation Levels <span class="text-red-500">*</span>
            </label>
            <div class="grid grid-cols-2 gap-3">
              <label 
                v-for="option in spaceLevelOptions" 
                :key="option" 
                class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 cursor-pointer transition-all"
              >
                <input
                  type="checkbox"
                  :value="option"
                  v-model="formData.spaceLevels"
                  class="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span class="text-sm font-medium text-gray-700">{{ option }}</span>
              </label>
            </div>
            <p v-if="errors.spaceLevels" class="mt-2 text-sm text-red-600 flex items-center">
              <AlertCircle class="w-4 h-4 mr-1" />
              {{ errors.spaceLevels }}
            </p>
          </div>

          <!-- Season Preference -->
          <div>
            <label class="block text-lg font-semibold text-gray-800 mb-4">
              Season Preference <span class="text-red-500">*</span>
            </label>
            <div class="flex flex-wrap gap-4">
              <label 
                v-for="option in ['in_season', 'pre_season']" 
                :key="option" 
                class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 cursor-pointer transition-all min-w-0 flex-1"
              >
                <input
                  type="radio"
                  name="seasonPreference"
                  :value="option"
                  v-model="formData.seasonPreference"
                  class="w-5 h-5 text-blue-600 focus:ring-blue-500"
                />
                <span class="text-sm font-medium text-gray-700 capitalize">{{ option.replace('_', ' ') }}</span>
              </label>
            </div>
            <p v-if="errors.seasonPreference" class="mt-2 text-sm text-red-600 flex items-center">
              <AlertCircle class="w-4 h-4 mr-1" />
              {{ errors.seasonPreference }}
            </p>
                      <!-- Conditional Recommendation Scope -->
          <div v-if="formData.seasonPreference === 'in_season'" class="border-l-4 border-blue-500 bg-blue-50 p-4 mt-4 rounded-r-lg">
            <label class="block text-lg font-semibold text-gray-800 mb-4">
              Recommendation Scope <span class="text-red-500">*</span>
            </label>
            <div class="flex flex-wrap gap-4">
              <label 
                v-for="option in ['space', 'space_range']" 
                :key="option" 
                class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-white cursor-pointer transition-all bg-white min-w-0 flex-1"
              >
                <input
                  type="radio"
                  name="recommendationScope"
                  :value="option"
                  v-model="formData.recommendationScope"
                  class="w-5 h-5 text-blue-600 border-gray-300 focus:ring-blue-500"
                />
                <span class="text-sm font-medium text-gray-700 capitalize">{{ option.replace('_', ' + ') }}</span>
              </label>
            </div>
            <p v-if="errors.recommendationScope" class="mt-2 text-sm text-red-600 flex items-center">
              <AlertCircle class="w-4 h-4 mr-1" />
              {{ errors.recommendationScope }}
            </p>
          </div>
          </div>



          <!-- Row 2: Performance Metric & Space Exchange Departments -->
          <div>
            <label class="block text-lg font-semibold text-gray-800 mb-4">
              Performance Metric <span class="text-red-500">*</span>
            </label>
            <div class="grid grid-cols-2 gap-3">
              <label 
                v-for="option in performanceMetricOptions" 
                :key="option" 
                class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 cursor-pointer transition-all"
              >
                <input
                  type="radio"
                  name="performanceMetric"
                  :value="option"
                  v-model="formData.performanceMetric"
                  class="w-5 h-5 text-blue-600 border-gray-300 focus:ring-blue-500"
                />
                <span class="text-sm font-medium text-gray-700">{{ option }}</span>
              </label>
            </div>
            <p v-if="errors.performanceMetric" class="mt-2 text-sm text-red-600 flex items-center">
              <AlertCircle class="w-4 h-4 mr-1" />
              {{ errors.performanceMetric }}
            </p>
          </div>

          <div>
            <label class="block text-lg font-semibold text-gray-800 mb-4">
              Space Exchange Departments <span class="text-red-500">*</span>
            </label>
            <div class="flex flex-wrap gap-4">
              <label 
                v-for="option in ['adjacent', 'others']" 
                :key="option" 
                class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 cursor-pointer transition-all min-w-0 flex-1"
              >
                <input
                  type="radio"
                  name="spaceExchangeDepartments"
                  :value="option"
                  v-model="formData.spaceExchangeDepartments"
                  class="w-5 h-5 text-blue-600 border-gray-300 focus:ring-blue-500"
                />
                <span class="text-sm font-medium text-gray-700 capitalize">{{ option }}</span>
              </label>
            </div>
            <p v-if="errors.spaceExchangeDepartments" class="mt-2 text-sm text-red-600 flex items-center">
              <AlertCircle class="w-4 h-4 mr-1" />
              {{ errors.spaceExchangeDepartments }}
            </p>
          </div>

          <!-- Row 3: Recommendation Period & Total Space Allocated -->
          <div>
            <label class="block text-lg font-semibold text-gray-800 mb-2">
              Recommendation Period <span class="text-red-500">*</span>
            </label>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-600 mb-2">Start Date</label>
                <input
                  type="date"
                  v-model="formData.recommendationPeriod.start"
                  class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-600 mb-2">End Date</label>
                <input
                  type="date"
                  v-model="formData.recommendationPeriod.end"
                  class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900"
                />
              </div>
            </div>
            <div v-if="formatDateRange()" class="mt-3 p-3 bg-blue-50 rounded-lg flex items-center">
              <Clock class="w-5 h-5 text-blue-600 mr-2" />
              <span class="text-sm font-medium text-blue-800">Selected Period: {{ formatDateRange() }}</span>
            </div>
            <p v-if="errors.recommendationPeriod" class="mt-2 text-sm text-red-600 flex items-center">
              <AlertCircle class="w-4 h-4 mr-1" />
              {{ errors.recommendationPeriod }}
            </p>
          </div>

          <div>
            <label class="block text-lg font-semibold text-gray-800 mb-4">
              Total Space Allocated <span class="text-red-500">*</span>
            </label>
            <div class="flex mt-4 flex-wrap gap-4">
              <label 
                v-for="option in ['constant', 'overall_reduction']" 
                :key="option" 
                class="flex mt-5 items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 cursor-pointer transition-all min-w-0 flex-1"
              >
                <input
                  type="radio"
                  name="totalSpaceAllocated"
                  :value="option"
                  v-model="formData.totalSpaceAllocated"
                  class="w-5 h-5 text-blue-600 border-gray-300 focus:ring-blue-500"
                />
                <span class="text-sm font-medium text-gray-700 capitalize">{{ option.replace('_', ' ') }}</span>
              </label>
            </div>
            <p v-if="errors.totalSpaceAllocated" class="mt-2 text-sm text-red-600 flex items-center">
              <AlertCircle class="w-4 h-4 mr-1" />
              {{ errors.totalSpaceAllocated }}
            </p>
          </div>
        </div>



        <!-- File Upload Section -->
        <div class="bg-gray-50 p-6 rounded-lg mt-8">
          <h3 class="text-lg font-semibold text-gray-800 mb-6 border-b border-gray-200 pb-2">
            File Uploads <span class="text-red-500">*</span>
          </h3>
          
          <div class="grid grid-cols-3 gap-6">
            <div v-for="{ key, label } in fileUploadFields" :key="key">
              <label class="block text-sm font-semibold text-gray-700 mb-3">
                {{ label }} <span class="text-red-500">*</span>
              </label>
              <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 hover:bg-blue-50 transition-all cursor-pointer">
                <Upload class="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <div class="text-sm text-gray-600">
                  <label class="cursor-pointer">
                    <span class="text-blue-600 hover:text-blue-700 font-semibold">
                      Click to upload file
                    </span>
                    <input
                      type="file"
                      class="hidden"
                      @change="(e) => handleFileUpload(key, e)"
                      accept=".csv,.xlsx,.xls"
                    />
                  </label>
                  <p class="text-xs text-gray-500 mt-1">
                    Supported: CSV, Excel files only
                  </p>
                </div>
                <div v-if="formData[key]" class="mt-2 flex items-center justify-center text-sm text-green-600 bg-green-50 p-2 rounded">
                  <CheckCircle class="w-4 h-4 mr-2" />
                  <span class="font-medium">{{ formData[key].name }}</span>
                </div>
              </div>
              <p v-if="errors[key]" class="mt-2 text-sm text-red-600 flex items-center">
                <AlertCircle class="w-4 h-4 mr-1" />
                {{ errors[key] }}
              </p>
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="mt-12 pt-8 border-t border-gray-200 text-center">
          <button
            @click="handleSubmit"
            class="px-12 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            Submit Space Recommendation
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Upload, CheckCircle, AlertCircle, Clock } from 'lucide-vue-next'

interface FormData {
  spaceLevels: string[]
  performanceMetric: string
  recommendationPeriod: { start: string; end: string }
  seasonPreference: string
  recommendationScope: string
  spaceExchangeDepartments: string
  totalSpaceAllocated: string
  exclusionItems: File | null
  minItemCount: File | null
  maxLinearMeterThreshold: File | null
}

interface Errors {
  [key: string]: string
}

const formData = reactive<FormData>({
  spaceLevels: [],
  performanceMetric: '',
  recommendationPeriod: { start: '', end: '' },
  seasonPreference: '',
  recommendationScope: '',
  spaceExchangeDepartments: '',
  totalSpaceAllocated: '',
  exclusionItems: null,
  minItemCount: null,
  maxLinearMeterThreshold: null
})

const errors = ref<Errors>({})

const spaceLevelOptions = [
  'Group',
  'Department',
  'Class',
  'Subclass',
]

const performanceMetricOptions = [
  'Revenue',
  'Margin'
]

const fileUploadFields = [
  { key: 'exclusionItems', label: 'Exclusion Items' },
  { key: 'minItemCount', label: 'Min Item Count' },
  { key: 'maxLinearMeterThreshold', label: 'Max Linear Meter Threshold' }
]

const handleFileUpload = (field: keyof FormData, event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0] || null
  
  if (file) {
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]
    
    const fileExtension = file.name.toLowerCase().split('.').pop()
    const allowedExtensions = ['csv', 'xls', 'xlsx']
    
    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension || '')) {
      alert('Error: Only CSV and Excel files are allowed!')
      target.value = ''
      return
    }
  }
  
  formData[field] = file as never
  
  // Clear error for this field if file is uploaded
  if (file && errors.value[field]) {
    errors.value[field] = ''
  }
}

const validateForm = (): boolean => {
  const newErrors: Errors = {}
  
  if (formData.spaceLevels.length === 0) {
    newErrors.spaceLevels = 'Please select at least one space recommendation level'
  }
  if (!formData.performanceMetric) {
    newErrors.performanceMetric = 'Please select a performance metric'
  }
  if (!formData.recommendationPeriod.start || !formData.recommendationPeriod.end) {
    newErrors.recommendationPeriod = 'Please select both start and end dates'
  }
  if (!formData.seasonPreference) {
    newErrors.seasonPreference = 'Please select season preference'
  }
  if (formData.seasonPreference === 'in_season' && !formData.recommendationScope) {
    newErrors.recommendationScope = 'Please select recommendation scope'
  }
  if (!formData.spaceExchangeDepartments) {
    newErrors.spaceExchangeDepartments = 'Please select space exchange option'
  }
  if (!formData.totalSpaceAllocated) {
    newErrors.totalSpaceAllocated = 'Please select total space allocation option'
  }
  
  // Validate file uploads
  if (!formData.exclusionItems) {
    newErrors.exclusionItems = 'Please upload exclusion items file'
  }
  if (!formData.minItemCount) {
    newErrors.minItemCount = 'Please upload min item count file'
  }
  if (!formData.maxLinearMeterThreshold) {
    newErrors.maxLinearMeterThreshold = 'Please upload max linear meter threshold file'
  }
  
  errors.value = newErrors
  
  if (Object.keys(newErrors).length > 0) {
    alert('Please fill in all required fields and correct any errors before submitting.')
    return false
  }
  
  return true
}

const handleSubmit = () => {
  if (validateForm()) {
    console.log('Form submitted:', formData)
    alert('Space recommendation form submitted successfully!')
  }
}

const formatDateRange = (): string => {
  if (formData.recommendationPeriod.start && formData.recommendationPeriod.end) {
    const start = new Date(formData.recommendationPeriod.start).toLocaleDateString()
    const end = new Date(formData.recommendationPeriod.end).toLocaleDateString()
    return `${start} - ${end}`
  }
  return ''
}
</script>