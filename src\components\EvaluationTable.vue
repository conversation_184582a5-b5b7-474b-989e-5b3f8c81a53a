<template>
  <table class="min-w-full  text-sm border border-gray-300">
    <thead>
      <!-- Main Grouping Row -->
      <tr class="bg-[#e5eaf1]">
        <th rowspan="3" class="px-4 py-2 text-lg font-bold text-[#232837] text-center align-middle border-r border-gray-300">
          DALMA
        </th>
        <th colspan="6" class="px-4 py-2 text-lg font-bold text-[#232837] text-center border-r border-gray-300">
          Store Level (in Scope Depts) WTD
        </th>
        <th colspan="6" class="px-4 py-2 text-lg font-bold text-[#232837] text-center">
          12 Weeks Prior
        </th>
      </tr>

      <!-- Metric Type Row -->
      <tr class="bg-[#e5eaf1] border-t border-gray-300">
        <th colspan="3" class="px-2 py-2 text-base font-semibold text-[#232837] text-center border-r border-gray-300">
          Revenue Growth
        </th>
        <th colspan="3" class="px-2 py-2 text-base font-semibold text-[#232837] text-center border-r border-gray-300">
          GMV Growth
        </th>
        <th colspan="3" class="px-2 py-2 text-base font-semibold text-[#232837] text-center border-r border-gray-300">
          Revenue Growth
        </th>
        <th colspan="3" class="px-2 py-2 text-base font-semibold text-[#232837] text-center">
          GMV Growth
        </th>
      </tr>

      <!-- Segment Subgroup Row -->
      <tr class="bg-[#e5eaf1] border-t border-gray-300">
        <!-- WTD Revenue Growth -->
        <th class="px-2 py-2 text-xs font-bold uppercase text-center">Home</th>
        <th class="px-2 py-2 text-xs font-bold uppercase text-center">Non-Home</th>
        <th class="px-2 py-2 text-xs font-bold uppercase text-center border-r border-gray-300">Overall</th>

        <!-- WTD GMV Growth -->
        <th class="px-2 py-2 text-xs font-bold uppercase text-center">Home</th>
        <th class="px-2 py-2 text-xs font-bold uppercase text-center">Non-Home</th>
        <th class="px-2 py-2 text-xs font-bold uppercase text-center border-r border-gray-300">Overall</th>

        <!-- Prior Revenue Growth -->
        <th class="px-2 py-2 text-xs font-bold uppercase text-center">Home</th>
        <th class="px-2 py-2 text-xs font-bold uppercase text-center">Non-Home</th>
        <th class="px-2 py-2 text-xs font-bold uppercase text-center border-r border-gray-300">Overall</th>

        <!-- Prior GMV Growth -->
        <th class="px-2 py-2 text-xs font-bold uppercase text-center">Home</th>
        <th class="px-2 py-2 text-xs font-bold uppercase text-center">Non-Home</th>
        <th class="px-2 py-2 text-xs font-bold uppercase text-center">Overall</th>
      </tr>
    </thead>

    <tbody>
      <tr v-for="row in tableData" :key="row.label" class="even:bg-[#f5faff] odd:bg-white">
        <td class="px-4 py-2 font-semibold text-center border-r border-gray-300">{{ row.label }}</td>
        <td
          v-for="(val, idx) in [
            row.wtd.revenue.home, row.wtd.revenue.nonHome, row.wtd.revenue.overall,
            row.wtd.gmv.home, row.wtd.gmv.nonHome, row.wtd.gmv.overall,
            row.prior.revenue.home, row.prior.revenue.nonHome, row.prior.revenue.overall,
            row.prior.gmv.home, row.prior.gmv.nonHome, row.prior.gmv.overall
          ]"
          :key="idx"
          :class="cellClass(idx)"
          class="px-2 py-2 text-center"
        >
          <span :class="growthClass(val)">
            <span v-if="isPositive(val)" class="inline-block align-middle text-green-400">▲</span>
            <span v-else-if="isNegative(val)" class="inline-block align-middle text-red-400">▼</span>
            <span class="inline-block align-middle">{{ val.replace('-', '').replace('+', '') }}</span>
          </span>
        </td>
      </tr>
    </tbody>
  </table>
</template>


<script setup lang="ts">
const props = defineProps<{ tableData: any[] }>();

function isPositive(val: string) {
  return val && !val.includes('-') && val !== '0%' && val !== '0';
}
function isNegative(val: string) {
  return val && val.includes('-') && val !== '0%' && val !== '0';
}
function growthClass(val: string) {
  if (isNegative(val)) return 'text-red-400';
  if (val === '0%' || val === '0') return 'text-gray-700';
  return 'text-green-400';
}
function cellClass(idx: number) {
  // Add vertical borders after each group
  // 2, 5, 8 are the last columns of each group
  if ([2, 5, 8].includes(idx)) return 'text-center border-r border-gray-300';
  return 'text-center';
}
</script> 