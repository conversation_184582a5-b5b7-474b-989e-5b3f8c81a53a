import { createRouter, createWebHistory } from 'vue-router';
import Optimization from './components/Optimization.vue';
import ProductivityChartsDashboard from './components/ProductivityChartsDashboard.vue';
import EvaluationDashboard from './components/EvaluationDashboard.vue';
import BusinessDashboard from './components/BusinessDashboard.vue';
import LandingPage from './views/LandingPage.vue';
import SpaceHealthDashboard from './components/SpaceHealthDashboard.vue';
import StoreCluster from './components/StoreCluster.vue';
import ConceptDashboard from './components/ConceptDashboard.vue';
import AuthCallback from './components/AuthCallback.vue';

const routes = [
  { path: '/', name: 'landing', component: LandingPage },
  { path: '/saturation-point', component: ProductivityChartsDashboard },
  { path: '/evaluation', component: EvaluationDashboard },
  { path: '/business-requirements',name: 'business-requirements', component: BusinessDashboard },
  { path: '/space-health', component: SpaceHealthDashboard },
  { path: '/store-clustering', name: 'store-clustering', component: StoreCluster },
  { path: '/concept-selection',name: 'concept-selection', component: ConceptDashboard },
  { path: '/optimization', component: Optimization },
  { path: '/callback', component: AuthCallback },
];

const router = createRouter({
  history: createWebHistory('/spaceoptimization/'), 
  routes,
});

export default router;